<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>#if(isEdit):Edit#else:Create#endif Consent Form - Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Graphik:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            color: #1F2937;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 16px 24px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6B7280;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: #3B82F6;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #3B82F6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563EB;
        }
        
        .btn-secondary {
            background: #F3F4F6;
            color: #374151;
            border: 1px solid #D1D5DB;
        }
        
        .btn-secondary:hover {
            background: #E5E7EB;
        }
        
        .btn-success {
            background: #10B981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 84px);
            transition: grid-template-columns 0.3s ease;
        }
        
        .main-container.preview-visible {
            grid-template-columns: 300px 1fr 400px;
        }
        
        .sidebar {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow-y: auto;
            position: relative;
        }
        
        .builder-area {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow-y: auto;
            position: relative;
        }
        
        .preview-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow-y: auto;
            display: none;
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease;
        }
        
        .preview-panel.visible {
            display: block;
            opacity: 1;
            transform: translateX(0);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 6px;
            color: #374151;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .section-types {
            margin-bottom: 24px;
        }
        
        .section-type {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 2px dashed #D1D5DB;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: grab;
            transition: all 0.2s;
            background: #FAFAFA;
        }
        
        .section-type:hover {
            border-color: #3B82F6;
            background: #F0F9FF;
        }
        
        .section-type.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }
        
        .section-icon {
            font-size: 20px;
            width: 24px;
            text-align: center;
        }
        
        .section-info h4 {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 2px;
        }
        
        .section-info p {
            font-size: 12px;
            color: #6B7280;
        }
        
        .consent-builder {
            min-height: 400px;
        }
        
        .consent-settings {
            background: #F9FAFB;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .builder-content {
            border: 2px dashed #D1D5DB;
            border-radius: 8px;
            min-height: 300px;
            padding: 24px;
            position: relative;
        }
        
        .builder-empty {
            text-align: center;
            color: #6B7280;
            padding: 48px 24px;
        }
        
        .builder-empty h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #374151;
        }
        
        .consent-section {
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            position: relative;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .section-title {
            font-weight: 600;
            color: #1F2937;
        }
        
        .section-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .btn-danger {
            background: #EF4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #DC2626;
        }
        
        .section-content {
            font-size: 14px;
            color: #6B7280;
        }
        
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .loading-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            text-align: center;
        }
        
        .error-message {
            background: #FEF2F2;
            border: 1px solid #FECACA;
            color: #DC2626;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading" class="loading">
        <div class="loading-content">
            <div>Saving consent form...</div>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="breadcrumb">
                <a href="/admin/dashboard">Admin Dashboard</a>
                <span>›</span>
                <a href="/admin/consent-creator">Consent Creator</a>
                <span>›</span>
                <span>#if(isEdit):Edit#else:Create#endif Consent Form</span>
            </div>
            
            <div class="header-actions">
                <button onclick="togglePreview()" class="btn btn-secondary" id="previewToggleBtn">Show Preview</button>
                <button onclick="saveConsentForm()" class="btn btn-success">Save Consent Form</button>
                <a href="/admin/consent-creator" class="btn btn-secondary">Cancel</a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h3 style="margin-bottom: 16px;">Consent Form Settings</h3>
            
            <!-- Error Message -->
            <div id="errorMessage" class="error-message"></div>
            
            <!-- Basic Settings -->
            <div class="form-group">
                <label class="form-label" for="consentName">Form Name</label>
                <input type="text" id="consentName" class="form-input" placeholder="Enter consent form name" #if(template):value="#(template.name)"#endif>
            </div>
            
            
            
            <div class="settings-grid">
                <div class="form-group">
                    <label class="form-label" for="consentStatus">Status</label>
                    <select id="consentStatus" class="form-select">
                        <option value="draft" #if(template):#{if(template.status == "draft")}selected#{endif}#else:selected#endif>Draft</option>
                        <option value="active" #if(template):#{if(template.status == "active")}selected#{endif}#endif>Active</option>
                        <option value="inactive" #if(template):#{if(template.status == "inactive")}selected#{endif}#endif>Inactive</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="consentLanguage">Language</label>
                    <select id="consentLanguage" class="form-select">
                        <option value="en" #if(template):#{if(template.language == "en")}selected#{endif}#else:selected#endif>English</option>
                        <option value="es" #if(template):#{if(template.language == "es")}selected#{endif}#endif>Spanish</option>
                        <option value="fr" #if(template):#{if(template.language == "fr")}selected#{endif}#endif>French</option>
                    </select>
                </div>
            </div>
            
            <hr style="margin: 24px 0; border: none; border-top: 1px solid #E5E7EB;">
            
            <h4 style="margin-bottom: 16px;">Section Types</h4>
            <div class="section-types">
                <div class="section-type" draggable="true" data-type="text">
                    <div class="section-icon">📝</div>
                    <div class="section-info">
                        <h4>Text Section</h4>
                        <p>Add informational text content</p>
                    </div>
                </div>
                
                <div class="section-type" draggable="true" data-type="list">
                    <div class="section-icon">📋</div>
                    <div class="section-info">
                        <h4>List Section</h4>
                        <p>Add bulleted list content</p>
                    </div>
                </div>
                
                <div class="section-type" draggable="true" data-type="signature">
                    <div class="section-icon">✍️</div>
                    <div class="section-info">
                        <h4>Signature Block</h4>
                        <p>Add signature and date fields</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Builder Area -->
        <div class="builder-area">
            <h3 style="margin-bottom: 16px;">Consent Form Builder</h3>
            
            <!-- Consent Settings -->
            <div class="consent-settings">
                <h4 style="margin-bottom: 12px;">Header & Styling</h4>
                <div class="settings-grid">
                    <div class="form-group">
                        <label class="form-label" for="headerTitle">Title</label>
                        <input type="text" id="headerTitle" class="form-input" placeholder="Consent Form Title">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="headerSubtitle">Subtitle</label>
                        <input type="text" id="headerSubtitle" class="form-input" placeholder="Optional subtitle">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="headerOrganization">Organization</label>
                        <input type="text" id="headerOrganization" class="form-input" placeholder="Organization name">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="primaryColor">Primary Color</label>
                        <input type="color" id="primaryColor" class="form-input" value="#3B82F6">
                    </div>
                </div>
            </div>
            
            <!-- Builder Content -->
            <div class="consent-builder">
                <div id="builderContent" class="builder-content">
                    <div class="builder-empty">
                        <h3>Start Building Your Consent Form</h3>
                        <p>Drag section types from the sidebar to add content to your consent form</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="preview-panel">
            <h3>Live Preview</h3>
            <div id="consentPreview" class="preview-consent">
                <div class="preview-title">Consent Form Preview</div>
                <p style="color: #6B7280; text-align: center;">Add sections to see preview</p>
            </div>
        </div>
    </div>

    <!-- Template data for editing -->
    #if(template):
    <script type="application/json" id="templateData">#unsafeHTML(template.templateJson)</script>
    #endif

    <script>
        // Global consent form data
        let consentData = {
            header: {
                title: '',
                subtitle: '',
                organization: ''
            },
            styling: {
                fontFamily: 'Arial, sans-serif',
                primaryColor: '#3B82F6'
            },
            metadata: {
                id: '',
                version: '1.0',
                description: '',
                fileNamePrefix: ''
            },
            sections: [],
            signature: {
                label: 'Signature',
                required: true,
                dateLabel: 'Date',
                signatureLineText: 'Participant Signature',
                participantNameLabel: 'Participant Name'
            }
        };

        let currentSectionId = 1;

        // Load template data if editing
        #if(template):
        try {
            const templateDataElement = document.getElementById('templateData');
            if (templateDataElement) {
                const rawTemplateJson = templateDataElement.textContent || templateDataElement.innerText || '';

                console.log('Loading template data...');
                console.log('Raw template JSON:', rawTemplateJson);

                if (!rawTemplateJson.trim()) {
                    throw new Error('Empty template data');
                }

                // Basic validation
                if (!rawTemplateJson.startsWith('{')) {
                    throw new Error('Invalid JSON format - does not start with {');
                }

                // Clean the JSON string
                const cleanedJson = rawTemplateJson.trim();

                // Try to identify the specific parsing issue
                console.log('Attempting to parse JSON...');
                console.log('JSON starts with:', cleanedJson.substring(0, 50));
                console.log('JSON ends with:', cleanedJson.substring(cleanedJson.length - 50));

                // Try to handle potential double-encoding issues
                let templateData;
                try {
                    templateData = JSON.parse(cleanedJson);
                } catch (firstError) {
                    console.log('First parse failed, trying to handle double-encoding...');
                    // If it's double-encoded as a string, try parsing it as a string first
                    try {
                        const stringData = JSON.parse(cleanedJson);
                        if (typeof stringData === 'string') {
                            templateData = JSON.parse(stringData);
                            console.log('Successfully parsed double-encoded JSON');
                        } else {
                            throw firstError;
                        }
                    } catch (secondError) {
                        console.error('Both parsing attempts failed');
                        throw firstError;
                    }
                }

                console.log('Successfully parsed template data:', templateData);

                // Normalize data structure
                if (templateData.header) consentData.header = { ...consentData.header, ...templateData.header };
                if (templateData.styling) consentData.styling = { ...consentData.styling, ...templateData.styling };
                if (templateData.metadata) consentData.metadata = { ...consentData.metadata, ...templateData.metadata };
                if (templateData.sections) consentData.sections = templateData.sections;
                if (templateData.signature) consentData.signature = { ...consentData.signature, ...templateData.signature };

                // Populate form fields
                populateFormFields();

                // Update ID counters based on existing data to prevent conflicts
                updateIdCounters();

                updatePreview();
                renderBuilder();
            }
        } catch (e) {
            console.error('Error parsing template data:', e);

            // Show user-friendly error message
            const errorEl = document.getElementById('errorMessage');
            if (errorEl) {
                errorEl.textContent = 'Error loading template data. The template may be corrupted. Starting with empty consent form.';
                errorEl.style.display = 'block';
            }

            // Continue with empty consent form
            console.log('Continuing with empty consent form structure');
        }
        #endif

        // Initialize drag and drop functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeDragAndDrop();
            updatePreview();
            setupEventListeners();

            // Add keyboard shortcut for preview toggle (Ctrl/Cmd + P)
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    togglePreview();
                }
            });
        });

        function populateFormFields() {
            // Populate basic form fields
            if (consentData.header.title) document.getElementById('headerTitle').value = consentData.header.title;
            if (consentData.header.subtitle) document.getElementById('headerSubtitle').value = consentData.header.subtitle;
            if (consentData.header.organization) document.getElementById('headerOrganization').value = consentData.header.organization;
            if (consentData.styling.primaryColor) document.getElementById('primaryColor').value = consentData.styling.primaryColor;
        }

        function updateIdCounters() {
            // Find the highest section ID to prevent conflicts
            let maxId = 0;
            consentData.sections.forEach(section => {
                if (section.id) {
                    const idNum = parseInt(section.id.replace('section_', ''));
                    if (!isNaN(idNum) && idNum > maxId) {
                        maxId = idNum;
                    }
                }
            });
            currentSectionId = maxId + 1;
        }

        function initializeDragAndDrop() {
            const sectionTypes = document.querySelectorAll('.section-type');
            const builderContent = document.getElementById('builderContent');

            // Make section types draggable
            sectionTypes.forEach(type => {
                type.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', this.dataset.type);
                    this.classList.add('dragging');
                });

                type.addEventListener('dragend', function(e) {
                    this.classList.remove('dragging');
                });
            });

            // Make builder area droppable
            builderContent.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.style.borderColor = '#3B82F6';
                this.style.backgroundColor = '#F0F9FF';
            });

            builderContent.addEventListener('dragleave', function(e) {
                this.style.borderColor = '#D1D5DB';
                this.style.backgroundColor = 'transparent';
            });

            builderContent.addEventListener('drop', function(e) {
                e.preventDefault();
                this.style.borderColor = '#D1D5DB';
                this.style.backgroundColor = 'transparent';

                const sectionType = e.dataTransfer.getData('text/plain');
                if (sectionType) {
                    addSection(sectionType);
                }
            });

            // Make sections sortable
            new Sortable(builderContent, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                onEnd: function(evt) {
                    // Reorder sections in data
                    const movedSection = consentData.sections.splice(evt.oldIndex, 1)[0];
                    consentData.sections.splice(evt.newIndex, 0, movedSection);
                    updatePreview();
                }
            });
        }

        function addSection(type) {
            const section = {
                id: `section_${currentSectionId++}`,
                type: type,
                title: '',
                content: type === 'list' ? [] : '',
                emphasis: false,
                required: false
            };

            // Set default values based on type
            switch (type) {
                case 'text':
                    section.title = 'Text Section';
                    section.content = 'Enter your text content here...';
                    break;
                case 'list':
                    section.title = 'List Section';
                    section.content = ['List item 1', 'List item 2'];
                    break;
                case 'signature':
                    section.title = 'Signature';
                    section.content = 'Please sign below to indicate your consent.';
                    break;
            }

            consentData.sections.push(section);
            renderBuilder();
            updatePreview();
        }

        function renderBuilder() {
            const builderContent = document.getElementById('builderContent');

            if (consentData.sections.length === 0) {
                builderContent.innerHTML = `
                    <div class="builder-empty">
                        <h3>Start Building Your Consent Form</h3>
                        <p>Drag section types from the sidebar to add content to your consent form</p>
                    </div>
                `;
                return;
            }

            let html = '';
            consentData.sections.forEach((section, index) => {
                html += renderSectionBuilder(section, index);
            });

            builderContent.innerHTML = html;
        }

        function renderSectionBuilder(section, index) {
            const typeIcon = {
                'text': '📝',
                'list': '📋',
                'signature': '✍️'
            };

            let contentHtml = '';
            if (section.type === 'list') {
                contentHtml = `
                    <div class="form-group">
                        <label class="form-label">List Items (one per line)</label>
                        <textarea class="form-input form-textarea" onchange="updateSectionContent(${index}, this.value.split('\\n').filter(item => item.trim()))">${Array.isArray(section.content) ? section.content.join('\n') : ''}</textarea>
                    </div>
                `;
            } else {
                contentHtml = `
                    <div class="form-group">
                        <label class="form-label">Content</label>
                        <textarea class="form-input form-textarea" onchange="updateSectionContent(${index}, this.value)">${section.content || ''}</textarea>
                    </div>
                `;
            }

            return `
                <div class="consent-section" data-section-index="${index}">
                    <div class="section-header">
                        <div class="section-title">
                            ${typeIcon[section.type] || '📄'} ${section.title}
                        </div>
                        <div class="section-actions">
                            <button onclick="editSection(${index})" class="btn btn-secondary btn-small">✏️ Edit</button>
                            <button onclick="deleteSection(${index})" class="btn btn-danger btn-small">🗑️ Delete</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Section Title</label>
                        <input type="text" class="form-input" value="${section.title}" onchange="updateSectionTitle(${index}, this.value)">
                    </div>

                    ${contentHtml}

                    <div style="display: flex; gap: 16px; margin-top: 12px;">
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px;">
                            <input type="checkbox" ${section.emphasis ? 'checked' : ''} onchange="updateSectionEmphasis(${index}, this.checked)">
                            Emphasize this section
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px;">
                            <input type="checkbox" ${section.required ? 'checked' : ''} onchange="updateSectionRequired(${index}, this.checked)">
                            Required section
                        </label>
                    </div>
                </div>
            `;
        }

        function setupEventListeners() {
            // Header and styling inputs
            document.getElementById('consentName').addEventListener('input', function() {
                consentData.header.title = this.value;

                // Auto-generate key from name
                const generatedKey = generateKeyFromName(this.value);
                document.getElementById('consentKey').value = generatedKey;

                updatePreview();
            });

            document.getElementById('headerTitle').addEventListener('input', function() {
                consentData.header.title = this.value;
                updatePreview();
            });

            document.getElementById('headerSubtitle').addEventListener('input', function() {
                consentData.header.subtitle = this.value;
                updatePreview();
            });

            document.getElementById('headerOrganization').addEventListener('input', function() {
                consentData.header.organization = this.value;
                updatePreview();
            });

            document.getElementById('primaryColor').addEventListener('input', function() {
                consentData.styling.primaryColor = this.value;
                updatePreview();
            });
        }

        function generateKeyFromName(name) {
            return name
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')    // Remove special characters except spaces and hyphens
                .replace(/\s+/g, '_')            // Replace spaces with underscores
                .replace(/-+/g, '_')             // Replace hyphens with underscores
                .replace(/_+/g, '_')             // Replace multiple underscores
                .replace(/^_|_$/g, '');          // Remove leading/trailing underscores
        }

        function updateSectionTitle(index, title) {
            consentData.sections[index].title = title;
            updatePreview();
        }

        function updateSectionContent(index, content) {
            consentData.sections[index].content = content;
            updatePreview();
        }

        function updateSectionEmphasis(index, emphasis) {
            consentData.sections[index].emphasis = emphasis;
            updatePreview();
        }

        function updateSectionRequired(index, required) {
            consentData.sections[index].required = required;
            updatePreview();
        }

        function editSection(index) {
            // For now, just focus on the section - could add modal editing later
            const section = document.querySelector(`[data-section-index="${index}"]`);
            if (section) {
                section.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        function deleteSection(index) {
            if (confirm('Delete this section?')) {
                consentData.sections.splice(index, 1);
                renderBuilder();
                updatePreview();
            }
        }

        function togglePreview() {
            const mainContainer = document.querySelector('.main-container');
            const previewPanel = document.querySelector('.preview-panel');
            const toggleBtn = document.getElementById('previewToggleBtn');

            if (previewPanel.classList.contains('visible')) {
                // Hide preview
                previewPanel.classList.remove('visible');
                mainContainer.classList.remove('preview-visible');
                toggleBtn.textContent = 'Show Preview';
            } else {
                // Show preview
                previewPanel.classList.add('visible');
                mainContainer.classList.add('preview-visible');
                toggleBtn.textContent = 'Hide Preview';
                // Update preview content when showing
                updatePreview();
            }
        }

        function updatePreview() {
            const previewArea = document.getElementById('consentPreview');

            if (consentData.sections.length === 0) {
                previewArea.innerHTML = `
                    <div class="preview-title">Consent Form Preview</div>
                    <p style="color: #6B7280; text-align: center;">Add sections to see preview</p>
                `;
                return;
            }

            let html = `
                <div style="max-width: 600px; margin: 0 auto; font-family: ${consentData.styling.fontFamily};">
                    <div style="text-align: center; margin-bottom: 24px; padding-bottom: 16px; border-bottom: 2px solid ${consentData.styling.primaryColor};">
                        <h1 style="color: ${consentData.styling.primaryColor}; margin-bottom: 8px;">${consentData.header.title || 'Consent Form'}</h1>
                        ${consentData.header.subtitle ? `<h2 style="color: #6B7280; font-size: 16px; margin-bottom: 8px;">${consentData.header.subtitle}</h2>` : ''}
                        ${consentData.header.organization ? `<p style="color: #6B7280; font-size: 14px;">${consentData.header.organization}</p>` : ''}
                    </div>
            `;

            consentData.sections.forEach(section => {
                html += renderPreviewSection(section);
            });

            html += '</div>';
            previewArea.innerHTML = html;
        }

        function renderPreviewSection(section) {
            const emphasisStyle = section.emphasis ? 'font-weight: bold; background: #F0F9FF; padding: 12px; border-left: 4px solid ' + consentData.styling.primaryColor + ';' : '';
            const requiredMark = section.required ? '<span style="color: red;">*</span>' : '';

            let html = `
                <div style="margin-bottom: 24px; ${emphasisStyle}">
                    <h3 style="color: #1F2937; margin-bottom: 12px;">${section.title}${requiredMark}</h3>
            `;

            if (section.type === 'text') {
                html += `<p style="color: #374151; line-height: 1.6;">${section.content}</p>`;
            } else if (section.type === 'list') {
                html += '<ul style="color: #374151; line-height: 1.6; padding-left: 20px;">';
                if (Array.isArray(section.content)) {
                    section.content.forEach(item => {
                        html += `<li style="margin-bottom: 4px;">${item}</li>`;
                    });
                }
                html += '</ul>';
            } else if (section.type === 'signature') {
                html += `
                    <p style="color: #374151; line-height: 1.6; margin-bottom: 16px;">${section.content}</p>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-top: 24px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">${consentData.signature.participantNameLabel}</label>
                            <div style="border-bottom: 1px solid #D1D5DB; height: 40px;"></div>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">${consentData.signature.dateLabel}</label>
                            <div style="border-bottom: 1px solid #D1D5DB; height: 40px;"></div>
                        </div>
                    </div>
                    <div style="margin-top: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">${consentData.signature.signatureLineText}</label>
                        <div style="border-bottom: 1px solid #D1D5DB; height: 40px;"></div>
                    </div>
                `;
            }

            html += '</div>';
            return html;
        }

        async function saveConsentForm() {
            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('errorMessage');

            // Hide any previous errors
            errorEl.style.display = 'none';

            // Validate required fields
            const name = document.getElementById('consentName').value.trim();
            if (!name) {
                errorEl.textContent = 'Please enter a consent form name.';
                errorEl.style.display = 'block';
                return;
            }

            // Prepare form data
            const formData = {
                name: name,
                key: generateKeyFromName(name), // Auto-generate key from name
                status: document.getElementById('consentStatus').value,
                language: document.getElementById('consentLanguage').value,
                templateJson: JSON.stringify(consentData)
            };

            try {
                loadingEl.style.display = 'flex';

                const url = #if(isEdit):'/admin/consent-creator/#(template.id)'#else:'/admin/consent-creator/create'#endif;
                const method = #if(isEdit):'PUT'#else:'POST'#endif;

                console.log('Saving consent form:', { url, method, formData });

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    console.log('Consent form saved successfully');
                    // Redirect to dashboard on success
                    window.location.href = '/admin/consent-creator';
                } else {
                    console.error('Save failed with status:', response.status);
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    throw new Error(`Failed to save consent form (${response.status}): ${errorText}`);
                }
            } catch (error) {
                console.error('Error saving consent form:', error);
                errorEl.textContent = 'Error saving consent form: ' + error.message;
                errorEl.style.display = 'block';
            } finally {
                loadingEl.style.display = 'none';
            }
        }
    </script>
</body>
</html>
