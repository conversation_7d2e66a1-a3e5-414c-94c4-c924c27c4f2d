<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #E5E7EB;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #FD8205, #E97100);
            border-radius: 8px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-icon::before {
            content: "✚";
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .user-info {
            color: #6B7280;
            font-size: 14px;
        }
        
        .logout-btn {
            padding: 8px 16px;
            background: #EF4444;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: background 0.2s;
        }
        
        .logout-btn:hover {
            background: #DC2626;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            font-size: 16px;
            color: #6B7280;
            margin-bottom: 40px;
        }
        
        .org-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .org-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 24px;
            transition: all 0.2s;
            border: 1px solid #E5E7EB;
        }
        
        .org-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .org-name {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 12px;
        }
        
        .org-id {
            font-size: 12px;
            color: #9CA3AF;
            font-family: monospace;
            margin-bottom: 20px;
        }
        
        .manage-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(to bottom, #FD8205, #E97100);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.2s;
        }
        
        .manage-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(253, 130, 5, 0.3);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6B7280;
        }
        
        .empty-state h3 {
            font-size: 20px;
            margin-bottom: 8px;
        }
        
        .empty-state p {
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-text">Wellup</div>
            </div>
            
            <div class="user-menu">
                <span class="user-info">Welcome, #(adminUsername)</span>
                <form method="POST" action="/admin/logout" style="display: inline;">
                    <button type="submit" class="logout-btn">Logout</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h1 class="page-title">Admin Dashboard</h1>
        <p class="page-subtitle">Manage your organization's configuration and assessment tools</p>

        <!-- Admin Tools Section -->
        <div style="margin-bottom: 40px;">
            <h2 style="font-size: 24px; font-weight: 600; color: #1F2937; margin-bottom: 20px;">Admin Tools</h2>
            <div class="org-grid">
                <div class="org-card">
                    <h3 class="org-name">Assessment Creator</h3>
                    <div class="org-id">Build and manage custom assessment templates with a visual form builder interface.</div>
                    <a href="/admin/assessment-creator" class="manage-btn">
                        Open Assessment Creator
                    </a>
                </div>
                                <div class="org-card">
                    <h3 class="org-name">Consent Creator</h3>
                    <div class="org-id">Create and manage digital consent forms with customizable sections and signature blocks.</div>
                    <a href="/admin/consent-creator" class="manage-btn">
                        Open Consent Creator
                    </a>
                </div>

                <div class="org-card">
                    <h3 class="org-name">Background Check Queue</h3>
                    <div class="org-id">Review and approve background check results that require manual verification for security compliance.</div>
                    <a href="/admin/background-checks/queue" class="manage-btn">
                        Review Queue
                    </a>
                </div>
            </div>
        </div>

        <!-- Organization Management Section -->
        <div>
            <h2 style="font-size: 24px; font-weight: 600; color: #1F2937; margin-bottom: 20px;">Organization Management</h2>
            <p style="color: #6B7280; margin-bottom: 20px;">Select an organization to manage their dropdown values</p>

            #if(count(organizations) > 0):
            <div class="org-grid">
                #for(org in organizations):
                <div class="org-card">
                    <h3 class="org-name">#(org.name)</h3>
                    <div class="org-id">Manage all your dropdowns, tags, roles, and other items across the platform in one place.</div>
                    <a href="/org/#(org.id)" class="manage-btn">
                        Manage Dropdowns
                    </a>
                </div>
                #endfor
            </div>
            #else:
            <div class="empty-state">
                <h3>No Organizations Found</h3>
                <p>There are no organizations available to manage.</p>
            </div>
            #endif
        </div>
    </div>
</body>
</html>
