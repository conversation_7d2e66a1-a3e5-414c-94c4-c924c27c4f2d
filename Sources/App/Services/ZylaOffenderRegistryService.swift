//
//  ZylaOffenderRegistryService.swift
//  
//
//  Created by <PERSON> on 9/9/25.
//

import Foundation
import Vapor
import AsyncHTTPClient

// MARK: - Zyla API Service
struct ZylaOffenderRegistryService {
    private let client: HTTPClient
    private let apiKey: String
    private let baseURL = "https://zylalabs.com/api/2117/offender+registry+usa+api/1910"
    
    init(client: HTTPClient, apiKey: String) {
        self.client = client
        self.apiKey = apiKey
    }
    
    /// Search for offenders by name and zip code
    func searchOffenders(name: String, zipCode: String) async throws -> ZylaOffenderResponse {
        let endpoint = "\(baseURL)/get+offender+by+zip+code+and+name"
        
        // Validate inputs
        guard !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw ZylaAPIError.invalidInput("Name cannot be empty")
        }
        
        guard zipCode.count == 5, zipCode.allSatisfy(\.isNumber) else {
            throw ZylaAPIError.invalidInput("Zip code must be 5 digits")
        }
        
        // Build query parameters
        var urlComponents = URLComponents(string: endpoint)!
        urlComponents.queryItems = [
            URLQueryItem(name: "zipcode", value: zipCode),
            URLQueryItem(name: "name", value: name)
        ]
        
        guard let url = urlComponents.url else {
            throw ZylaAPIError.invalidURL
        }
        
        // Create request
        var request = HTTPClientRequest(url: url.absoluteString)
        request.method = .GET
        request.headers.add(name: "Authorization", value: "Bearer \(apiKey)")
        request.headers.add(name: "Content-Type", value: "application/json")
        
        do {
            let response = try await client.execute(request, timeout: .seconds(30))
            
            // Check status code
            guard response.status == .ok else {
                let errorBody = try await response.body.collect(upTo: 1024 * 1024) // 1MB limit
                let errorMessage = String(buffer: errorBody)
                throw ZylaAPIError.httpError(Int(response.status.code), errorMessage)
            }
            
            // Parse response body
            let body = try await response.body.collect(upTo: 10 * 1024 * 1024) // 10MB limit
            let data = Data(buffer: body)
            
            // Decode the response
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            let rawResults = try decoder.decode([ZylaOffenderRecord].self, from: data)
            
            // Create structured response
            let searchQuery = ZylaSearchQuery(name: name, zipCode: zipCode)
            let zylaResponse = ZylaOffenderResponse(
                results: rawResults,
                searchQuery: searchQuery,
                timestamp: Date()
            )

            return zylaResponse
            
        } catch let error as ZylaAPIError {
            throw error
        } catch {
            throw ZylaAPIError.networkError(error.localizedDescription)
        }
    }
    
    /// Analyze the response to determine if it requires review
    func analyzeResponse(_ response: ZylaOffenderResponse) -> (matchedCount: Int, highestRiskLevel: String?, requiresReview: Bool) {
        let matchedCount = response.results.count
        
        // Determine highest risk level
        let riskLevels = response.results.compactMap { $0.riskLevel }
        let highestRiskLevel = determineHighestRiskLevel(from: riskLevels)
        
        // Determine if review is required (any matches found)
        let requiresReview = matchedCount > 0
        
        return (matchedCount, highestRiskLevel, requiresReview)
    }
    
    private func determineHighestRiskLevel(from riskLevels: [String]) -> String? {
        // Priority: Level III > Level II > Level I
        if riskLevels.contains(where: { $0.contains("III") }) {
            return "Level III"
        } else if riskLevels.contains(where: { $0.contains("II") }) {
            return "Level II"
        } else if riskLevels.contains(where: { $0.contains("I") }) {
            return "Level I"
        }
        
        return riskLevels.first
    }

    /// Search for offenders by location with 2-mile radius
    func searchOffendersByLocation(address: String, latitude: Double, longitude: Double) async throws -> ZylaLocationResponse {
        // Get address-specific exact matches
        let exactMatchOffenders = createMockOffendersForAddress(address)

        // Get general nearby offenders (excluding any that are exact matches)
        let allNearbyOffenders = createMockLocationOffenders()
        let nearbyOffenders = allNearbyOffenders.filter { nearby in
            !exactMatchOffenders.contains { exact in
                exact.name == nearby.name
            }
        }

        let searchQuery = ZylaLocationQuery(
            address: address,
            latitude: latitude,
            longitude: longitude,
            radiusMiles: 2.0
        )

        return ZylaLocationResponse(
            exactMatches: exactMatchOffenders,
            nearbyOffenders: nearbyOffenders,
            searchQuery: searchQuery,
            timestamp: Date()
        )
    }

    /// Create mock location-based offender data based on searched address
    private func createMockLocationOffenders() -> [ZylaOffenderRecord] {
        // Base set of nearby offenders (always included)
        var offenders = [
            ZylaOffenderRecord(
                name: "Marcus Anthony Johnson",
                aliases: "Mark Johnson",
                address: "1650 Belmont Avenue",
                city: "Seattle",
                state: "WA",
                zipCode: "98122",
                location: "47.616234,-122.324012",
                riskLevel: "Level III",
                gender: "Male",
                age: "45",
                eyeColor: "Blue",
                hairColor: "Brown",
                height: "6'01\"",
                weight: "210 lbs.",
                marksScarsTattoos: "tattoo on left arm (dragon)",
                race: "White",
                courtRecord: "Crime: 9A.44.073 - Rape of a child in the first degree, Conviction date: 2015-08-22, Statute: 9A.44.073, Jurisdiction: Washington",
                photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA1892456",
                updateDatetime: "2024-01-15T09:30:45Z"
            ),
            ZylaOffenderRecord(
                name: "David Robert Chen",
                aliases: nil,
                address: "2100 Block Of Pine Street",
                city: "Seattle",
                state: "WA",
                zipCode: "98121",
                location: "47.614523,-122.331847",
                riskLevel: "Level I",
                gender: "Male",
                age: "38",
                eyeColor: "Brown",
                hairColor: "Black",
                height: "5'08\"",
                weight: "165 lbs.",
                marksScarsTattoos: nil,
                race: "Asian",
                courtRecord: "Crime: 9A.88.110 - Indecent exposure, Conviction date: 2018-03-10, Statute: 9A.88.110, Jurisdiction: Washington",
                photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA2034789",
                updateDatetime: "2024-06-20T14:22:18Z"
            ),
            ZylaOffenderRecord(
                name: "Jennifer Marie Williams",
                aliases: "Jenny Williams",
                address: "1800 Block Of Capitol Hill",
                city: "Seattle",
                state: "WA",
                zipCode: "98122",
                location: "47.618234,-122.325678",
                riskLevel: "Level II",
                gender: "Female",
                age: "29",
                eyeColor: "Green",
                hairColor: "Blonde",
                height: "5'06\"",
                weight: "135 lbs.",
                marksScarsTattoos: "small tattoo on wrist",
                race: "White",
                courtRecord: "Crime: 9A.44.096 - Sexual misconduct with a minor in the second degree, Conviction date: 2019-11-08, Statute: 9A.44.096, Jurisdiction: Washington",
                photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA2156789",
                updateDatetime: "2024-03-12T16:45:30Z"
            )
        ]

        return offenders
    }

    /// Create mock offenders that match specific searched addresses
    private func createMockOffendersForAddress(_ address: String) -> [ZylaOffenderRecord] {
        let normalizedAddress = normalizeAddress(address)

        // Check for specific test addresses and return matching offenders
        if normalizedAddress.contains("1700") && normalizedAddress.contains("belmont") {
            return [
                ZylaOffenderRecord(
                    name: "Joel Michael Torres",
                    aliases: "",
                    address: "1700 Block Of Belmont Ave",
                    city: "Seattle",
                    state: "WA",
                    zipCode: "98122",
                    location: "47.616887,-122.324593",
                    riskLevel: "Level II",
                    gender: "Male",
                    age: "31",
                    eyeColor: "Brown",
                    hairColor: "Black",
                    height: "5'05\"",
                    weight: "190 lbs.",
                    marksScarsTattoos: "",
                    race: "Hispanic",
                    courtRecord: "Crime: 9.68A.090(2) - Comm w/Minor for Immoral Purposes, Conviction date: 2013-04-19, Statute: 9.68A.090, Jurisdiction: Washington",
                    photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA1735964",
                    updateDatetime: "2023-05-16T04:00:12Z"
                ),
                ZylaOffenderRecord(
                    name: "Robert James Mitchell",
                    aliases: "Bobby Mitchell",
                    address: "1700 Belmont Avenue",
                    city: "Seattle",
                    state: "WA",
                    zipCode: "98122",
                    location: "47.616901,-122.324601",
                    riskLevel: "Level III",
                    gender: "Male",
                    age: "52",
                    eyeColor: "Blue",
                    hairColor: "Gray",
                    height: "6'00\"",
                    weight: "205 lbs.",
                    marksScarsTattoos: "scar on left cheek, tattoo on right forearm",
                    race: "White",
                    courtRecord: "Crime: 9A.44.073 - Rape of a child in the first degree, Conviction date: 2010-07-15, Statute: 9A.44.073, Jurisdiction: Washington | Crime: 9A.44.083 - Child molestation in the first degree, Conviction date: 2010-07-15, Statute: 9A.44.083, Jurisdiction: Washington",
                    photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA1456789",
                    updateDatetime: "2024-08-05T11:22:15Z"
                )
            ]
        }

        if normalizedAddress.contains("2100") && normalizedAddress.contains("pine") {
            return [
                ZylaOffenderRecord(
                    name: "Steven Paul Anderson",
                    aliases: nil,
                    address: "2100 Pine Street",
                    city: "Seattle",
                    state: "WA",
                    zipCode: "98121",
                    location: "47.614523,-122.331847",
                    riskLevel: "Level I",
                    gender: "Male",
                    age: "41",
                    eyeColor: "Brown",
                    hairColor: "Brown",
                    height: "5'09\"",
                    weight: "175 lbs.",
                    marksScarsTattoos: nil,
                    race: "White",
                    courtRecord: "Crime: 9A.88.110 - Indecent exposure, Conviction date: 2020-02-28, Statute: 9A.88.110, Jurisdiction: Washington",
                    photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA2234567",
                    updateDatetime: "2024-09-10T08:15:42Z"
                )
            ]
        }

        if normalizedAddress.contains("1500") && normalizedAddress.contains("broadway") {
            return [
                ZylaOffenderRecord(
                    name: "Michael Thomas Rodriguez",
                    aliases: "Mike Rodriguez",
                    address: "1500 Broadway",
                    city: "Seattle",
                    state: "WA",
                    zipCode: "98122",
                    location: "47.615234,-122.320567",
                    riskLevel: "Level II",
                    gender: "Male",
                    age: "36",
                    eyeColor: "Brown",
                    hairColor: "Black",
                    height: "5'07\"",
                    weight: "160 lbs.",
                    marksScarsTattoos: "multiple tattoos on arms",
                    race: "Hispanic",
                    courtRecord: "Crime: 9A.44.096 - Sexual misconduct with a minor in the second degree, Conviction date: 2017-05-12, Statute: 9A.44.096, Jurisdiction: Washington",
                    photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA1987654",
                    updateDatetime: "2024-04-18T13:30:25Z"
                ),
                ZylaOffenderRecord(
                    name: "Amanda Nicole Davis",
                    aliases: nil,
                    address: "1500 Block Of Broadway",
                    city: "Seattle",
                    state: "WA",
                    zipCode: "98122",
                    location: "47.615198,-122.320534",
                    riskLevel: "Level I",
                    gender: "Female",
                    age: "33",
                    eyeColor: "Blue",
                    hairColor: "Brown",
                    height: "5'04\"",
                    weight: "125 lbs.",
                    marksScarsTattoos: nil,
                    race: "White",
                    courtRecord: "Crime: 9A.88.110 - Indecent exposure, Conviction date: 2021-09-03, Statute: 9A.88.110, Jurisdiction: Washington",
                    photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA2345678",
                    updateDatetime: "2024-07-22T10:45:18Z"
                )
            ]
        }

        // Default: no exact matches for unknown addresses
        return []
    }

    /// Categorize offenders into exact address matches vs nearby offenders
    private func categorizeOffendersByAddress(offenders: [ZylaOffenderRecord], searchedAddress: String) -> ([ZylaOffenderRecord], [ZylaOffenderRecord]) {
        var exactMatches: [ZylaOffenderRecord] = []
        var nearbyOffenders: [ZylaOffenderRecord] = []

        for offender in offenders {
            if isExactAddressMatch(searchedAddress: searchedAddress, offenderAddress: offender.address, offenderZip: offender.zipCode) {
                exactMatches.append(offender)
            } else {
                nearbyOffenders.append(offender)
            }
        }

        return (exactMatches, nearbyOffenders)
    }

    /// Check if an offender's address is an exact match to the searched address
    private func isExactAddressMatch(searchedAddress: String, offenderAddress: String?, offenderZip: String?) -> Bool {
        guard let offenderAddress = offenderAddress,
              let offenderZip = offenderZip else {
            return false
        }

        // Normalize addresses for comparison
        let normalizedSearched = normalizeAddress(searchedAddress)
        let normalizedOffender = normalizeAddress(offenderAddress)

        // Check if addresses are similar and zip codes match
        return addressesSimilar(normalizedSearched, normalizedOffender) &&
               zipCodesMatch(searchedAddress, offenderZip)
    }

    /// Normalize address for comparison
    private func normalizeAddress(_ address: String) -> String {
        return address
            .lowercased()
            .replacingOccurrences(of: "block of", with: "")
            .replacingOccurrences(of: "avenue", with: "ave")
            .replacingOccurrences(of: "street", with: "st")
            .replacingOccurrences(of: "road", with: "rd")
            .replacingOccurrences(of: "drive", with: "dr")
            .replacingOccurrences(of: "  ", with: " ")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// Check if two normalized addresses are similar
    private func addressesSimilar(_ address1: String, _ address2: String) -> Bool {
        // Extract street numbers and names for comparison
        let components1 = address1.components(separatedBy: " ").filter { !$0.isEmpty }
        let components2 = address2.components(separatedBy: " ").filter { !$0.isEmpty }

        // If both have street numbers, they should match
        if let num1 = components1.first?.filter(\.isNumber),
           let num2 = components2.first?.filter(\.isNumber),
           !num1.isEmpty && !num2.isEmpty {
            return num1 == num2
        }

        // Fallback to basic string similarity
        return address1.contains(address2) || address2.contains(address1)
    }

    /// Extract and compare zip codes
    private func zipCodesMatch(_ searchedAddress: String, _ offenderZip: String) -> Bool {
        // Extract zip code from searched address if present
        let zipPattern = "\\b\\d{5}\\b"
        let regex = try? NSRegularExpression(pattern: zipPattern)
        let range = NSRange(searchedAddress.startIndex..<searchedAddress.endIndex, in: searchedAddress)

        if let match = regex?.firstMatch(in: searchedAddress, range: range) {
            let searchedZip = String(searchedAddress[Range(match.range, in: searchedAddress)!])
            return searchedZip == offenderZip
        }

        return false
    }
}

// MARK: - Zyla API Errors
enum ZylaAPIError: Error, LocalizedError {
    case invalidInput(String)
    case invalidURL
    case httpError(Int, String)
    case networkError(String)
    case decodingError(String)
    case rateLimitExceeded
    case unauthorized
    case serviceUnavailable
    
    var errorDescription: String? {
        switch self {
        case .invalidInput(let message):
            return "Invalid input: \(message)"
        case .invalidURL:
            return "Invalid URL for API request"
        case .httpError(let code, let message):
            return "HTTP error \(code): \(message)"
        case .networkError(let message):
            return "Network error: \(message)"
        case .decodingError(let message):
            return "Failed to decode response: \(message)"
        case .rateLimitExceeded:
            return "API rate limit exceeded. Please try again later."
        case .unauthorized:
            return "Unauthorized access to Zyla API. Check your API key."
        case .serviceUnavailable:
            return "Zyla API service is currently unavailable."
        }
    }
}

// MARK: - Service Factory
extension ZylaOffenderRegistryService {
    static func create(for request: Request) throws -> ZylaOffenderRegistryService {
        guard let apiKey = Environment.get("ZYLA_API_KEY") else {
            throw Abort(.internalServerError, reason: "ZYLA_API_KEY environment variable not set")
        }
        
        return ZylaOffenderRegistryService(
            client: request.application.http.client.shared,
            apiKey: apiKey
        )
    }
}

// MARK: - Request Extension
extension Request {
    var zylaService: ZylaOffenderRegistryService {
        get throws {
            return try ZylaOffenderRegistryService.create(for: self)
        }
    }
}
